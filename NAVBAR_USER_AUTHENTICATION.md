# Navbar User Authentication

## Overview

Navbar telah diupdate untuk menampilkan status authentication user secara dinamis. Ketika user belum login, navbar menampilkan tombol Login dan Register. Ketika user sudah login, navbar menampilkan nama user dan tombol Logout.

## Features Implemented

### 1. Dynamic Navbar State
- **Not Logged In**: Menampilkan tombol "Login" dan "Register"
- **Logged In**: Menampilkan "Welcome, [Username]" dan tombol "Logout"
- **Loading State**: Menampilkan "Loading..." saat mengecek session

### 2. Session Management
- **Session Check**: Otomatis mengecek session saat navbar dimuat
- **Local Storage**: Menggunakan sessionId dan user data dari localStorage
- **API Validation**: Memvalidasi session dengan endpoint `/api/auth/me`
- **Auto Cleanup**: Menghapus invalid session dari localStorage

### 3. User Experience
- **Welcome Message**: Menampilkan "Welcome, [Username]" untuk user yang login
- **Logout Functionality**: Tombol logout yang membersihkan session dan redirect ke home
- **Seamless Navigation**: Navbar ter-update otomatis setelah login/logout

## Technical Implementation

### Navbar Component (`src/components/Navbar.tsx`)

#### State Management
```typescript
const [user, setUser] = useState<User | null>(null);
const [loading, setLoading] = useState(true);
```

#### Session Check Function
```typescript
const checkUserSession = async () => {
  const sessionId = localStorage.getItem('sessionId');
  if (!sessionId) return;
  
  const response = await fetch('/api/auth/me', {
    headers: { 'x-session-id': sessionId }
  });
  
  if (response.ok) {
    const data = await response.json();
    setUser(data.user);
  } else {
    // Clear invalid session
    localStorage.removeItem('sessionId');
    localStorage.removeItem('user');
  }
};
```

#### Logout Function
```typescript
const handleLogout = () => {
  localStorage.removeItem('sessionId');
  localStorage.removeItem('user');
  setUser(null);
  window.location.href = '/';
};
```

### Login Page Updates
- Changed `router.push('/')` to `window.location.href = '/'` untuk full page refresh
- Memastikan navbar ter-update setelah login berhasil

### Register Page Updates
- Changed `router.push()` to `window.location.href` untuk konsistensi
- Redirect ke login page dengan success message

## User Interface States

### 1. Not Authenticated
```
ATMA                           [Login] [Register]
```

### 2. Loading State
```
ATMA                           Loading...
```

### 3. Authenticated
```
ATMA                           Welcome, John Doe [Logout]
```

## User Flow

### Registration Flow
1. User mengisi form registrasi
2. Setelah sukses, redirect ke login page dengan success message
3. User login dengan credentials yang baru dibuat
4. Setelah login, navbar menampilkan nama user

### Login Flow
1. User mengisi form login
2. Setelah sukses, sessionId dan user data disimpan di localStorage
3. Redirect ke home page dengan full page refresh
4. Navbar otomatis menampilkan nama user

### Logout Flow
1. User klik tombol "Logout" di navbar
2. Session data dihapus dari localStorage
3. User state di-reset ke null
4. Redirect ke home page
5. Navbar kembali menampilkan tombol Login/Register

## Security Considerations

### Session Validation
- Session divalidasi dengan API call ke `/api/auth/me`
- Invalid session otomatis dibersihkan dari localStorage
- Tidak ada sensitive data yang disimpan di client-side

### Error Handling
- Graceful handling untuk network errors
- Fallback ke unauthenticated state jika terjadi error
- Console logging untuk debugging

## Testing

### Manual Testing Steps
1. **Initial State**: Buka http://localhost:3000, pastikan navbar menampilkan Login/Register
2. **Registration**: Register user baru, pastikan redirect ke login
3. **Login**: Login dengan user yang baru dibuat, pastikan navbar menampilkan nama user
4. **Logout**: Klik logout, pastikan navbar kembali ke state awal
5. **Session Persistence**: Refresh page saat login, pastikan user tetap login
6. **Invalid Session**: Hapus sessionId dari localStorage, pastikan navbar ter-update

### API Testing
```bash
# Test session validation
curl -H "x-session-id: valid-session-id" http://localhost:3000/api/auth/me

# Test invalid session
curl -H "x-session-id: invalid-session-id" http://localhost:3000/api/auth/me
```

## Browser Compatibility
- Menggunakan localStorage (supported di semua modern browsers)
- React hooks untuk state management
- Fetch API untuk HTTP requests
- Compatible dengan Next.js 15.3.5

## Future Enhancements
- User profile dropdown menu
- User avatar/profile picture
- Session timeout handling
- Remember me functionality
- Multiple device session management
