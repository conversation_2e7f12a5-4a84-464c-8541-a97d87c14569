'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import RadarChart from '@/components/RadarChart';
import LoadingSpinner from '@/components/LoadingSpinner';
import { useAssessmentStore } from '@/store';

interface RiasecScores {
  realistic: number;
  investigative: number;
  artistic: number;
  social: number;
  enterprising: number;
  conventional: number;
}

interface OceanScores {
  openness: number;
  conscientiousness: number;
  extraversion: number;
  agreeableness: number;
  neuroticism: number;
}

interface CategoryDescription {
  name: string;
  description: string;
  characteristics?: string[];
  careers?: string[];
  high_traits?: string[];
  low_traits?: string[];
}

interface Analysis {
  archetype: string;
  shortSummary: string;
  strengths: string[];
  careerSuggestions: string[];
  insights: string;
  weaknesses: string[];
  workEnvironment: string;
}

interface ResultsData {
  riasec: {
    scores: RiasecScores;
    descriptions: Record<string, CategoryDescription>;
  };
  ocean: {
    scores: OceanScores;
    descriptions: Record<string, CategoryDescription>;
  };
  analysis: Analysis;
}

export default function ResultsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session');

  const { getCachedResults, cacheResults } = useAssessmentStore();

  const [results, setResults] = useState<ResultsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<'riasec' | 'ocean' | 'summary'>('riasec');

  useEffect(() => {
    if (!sessionId) {
      router.push('/');
      return;
    }

    // Check cache immediately to avoid loading spinner
    const cachedResults = getCachedResults(sessionId);
    if (cachedResults) {
      setResults(cachedResults as any);
      setIsLoading(false);
      return;
    }

    // If no cache, then load from API
    loadResults();
  }, [sessionId, router, getCachedResults]);

  const loadResults = async () => {
    if (!sessionId) return;

    try {
      // Fetch from API (cache already checked in useEffect)
      const response = await fetch(`/api/results/${sessionId}`);
      const data = await response.json();

      if (data.success) {
        setResults(data);
        // Cache the results
        cacheResults(sessionId, data);
      } else {
        alert('Results not found or still processing');
        router.push('/');
      }
    } catch (error) {
      console.error('Error loading results:', error);
      alert('Failed to load results');
      router.push('/');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <LoadingSpinner size="lg" text="Analyzing your responses and generating insights..." />
      </div>
    );
  }

  if (!results) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No results found</p>
        </div>
      </div>
    );
  }

  // Prepare radar chart data
  const riasecChartData = [
    { category: 'Realistic', score: results.riasec.scores.realistic, fullMark: 100 },
    { category: 'Investigative', score: results.riasec.scores.investigative, fullMark: 100 },
    { category: 'Artistic', score: results.riasec.scores.artistic, fullMark: 100 },
    { category: 'Social', score: results.riasec.scores.social, fullMark: 100 },
    { category: 'Enterprising', score: results.riasec.scores.enterprising, fullMark: 100 },
    { category: 'Conventional', score: results.riasec.scores.conventional, fullMark: 100 },
  ];

  const oceanChartData = [
    { category: 'Openness', score: results.ocean.scores.openness, fullMark: 100 },
    { category: 'Conscientiousness', score: results.ocean.scores.conscientiousness, fullMark: 100 },
    { category: 'Extraversion', score: results.ocean.scores.extraversion, fullMark: 100 },
    { category: 'Agreeableness', score: results.ocean.scores.agreeableness, fullMark: 100 },
    { category: 'Neuroticism', score: results.ocean.scores.neuroticism, fullMark: 100 },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Your Talent Profile</h1>
          <p className="text-xl text-gray-600">
            Discover your unique combination of interests and personality traits
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-md">
            <button
              onClick={() => setActiveSection('riasec')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeSection === 'riasec'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              RIASEC
            </button>
            <button
              onClick={() => setActiveSection('ocean')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeSection === 'ocean'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              OCEAN
            </button>
            <button
              onClick={() => setActiveSection('summary')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeSection === 'summary'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Summary
            </button>
          </div>
        </div>

        {/* Content Sections */}
        {activeSection === 'riasec' && (
          <div className="space-y-8">
            <div className="grid lg:grid-cols-2 gap-8">
              <RadarChart 
                data={riasecChartData} 
                title="RIASEC Career Interests" 
                color="#4F46E5"
              />
              
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Your Scores</h3>
                <div className="space-y-4">
                  {Object.entries(results.riasec.scores).map(([key, score]) => {
                    const categoryKey = key.charAt(0).toUpperCase();
                    const description = results.riasec.descriptions[categoryKey];
                    return (
                      <div key={key} className="border-b border-gray-200 pb-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {description?.name || key}
                          </span>
                          <span className="text-indigo-600 font-semibold">{score}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-indigo-600 h-2 rounded-full"
                            style={{ width: `${score}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">{description?.description}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* RIASEC Category Details */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(results.riasec.descriptions).map(([key, description]) => (
                <div key={key} className="bg-white rounded-lg shadow-md p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">{description.name}</h4>
                  <p className="text-gray-600 mb-4 text-sm">{description.description}</p>
                  
                  {description.characteristics && (
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-900 mb-2">Characteristics:</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {description.characteristics.map((char, index) => (
                          <li key={index} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-indigo-600 rounded-full mr-2"></span>
                            {char}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {description.careers && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Career Examples:</h5>
                      <div className="flex flex-wrap gap-1">
                        {description.careers.map((career, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full"
                          >
                            {career}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeSection === 'ocean' && (
          <div className="space-y-8">
            <div className="grid lg:grid-cols-2 gap-8">
              <RadarChart 
                data={oceanChartData} 
                title="OCEAN Personality Traits" 
                color="#059669"
              />
              
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Your Scores</h3>
                <div className="space-y-4">
                  {Object.entries(results.ocean.scores).map(([key, score]) => {
                    const categoryKey = key.charAt(0).toUpperCase();
                    const description = results.ocean.descriptions[categoryKey];
                    return (
                      <div key={key} className="border-b border-gray-200 pb-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {description?.name || key}
                          </span>
                          <span className="text-emerald-600 font-semibold">{score}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-emerald-600 h-2 rounded-full"
                            style={{ width: `${score}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">{description?.description}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* OCEAN Category Details */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(results.ocean.descriptions).map(([key, description]) => (
                <div key={key} className="bg-white rounded-lg shadow-md p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">{description.name}</h4>
                  <p className="text-gray-600 mb-4 text-sm">{description.description}</p>
                  
                  {description.high_traits && (
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-900 mb-2">High Score Traits:</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {description.high_traits.map((trait, index) => (
                          <li key={index} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-2"></span>
                            {trait}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {description.low_traits && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Low Score Traits:</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {description.low_traits.map((trait, index) => (
                          <li key={index} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                            {trait}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeSection === 'summary' && (
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Archetype */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <h2 className="text-3xl font-bold text-indigo-600 mb-4">
                {results.analysis.archetype}
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                {results.analysis.shortSummary}
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Strengths */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 text-sm">✓</span>
                  </span>
                  Strengths
                </h3>
                <ul className="space-y-2">
                  {results.analysis.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-700">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Areas for Development */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-orange-600 text-sm">!</span>
                  </span>
                  Areas for Development
                </h3>
                <ul className="space-y-2">
                  {results.analysis.weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-700">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Career Suggestions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Career Suggestions</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                {results.analysis.careerSuggestions.map((career, index) => (
                  <div 
                    key={index}
                    className="px-4 py-2 bg-indigo-50 text-indigo-800 rounded-lg text-center font-medium"
                  >
                    {career}
                  </div>
                ))}
              </div>
            </div>

            {/* Insights */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Insights</h3>
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {results.analysis.insights}
                </p>
              </div>
            </div>

            {/* Work Environment */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Ideal Work Environment</h3>
              <p className="text-gray-700 leading-relaxed">
                {results.analysis.workEnvironment}
              </p>
            </div>

            {/* Action Button */}
            <div className="text-center">
              <button
                onClick={() => router.push('/')}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
              >
                Take Another Assessment
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
