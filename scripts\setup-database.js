require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
  const pool = new Pool({
    connectionString: process.env.POSTGRES_URL,
  });

  try {
    console.log('Setting up database...');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'src', 'lib', 'database-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    await pool.query(schema);
    
    console.log('Database setup completed successfully!');
    console.log('Tables created:');
    console.log('- users');
    console.log('- user_sessions');
    console.log('- riasec_questions');
    console.log('- ocean_questions');
    console.log('- riasec_responses');
    console.log('- ocean_responses');
    console.log('- assessment_scores');
    console.log('- analysis_results');
    console.log('');
    console.log('Sample questions inserted for both RIASEC and OCEAN assessments.');
    console.log('');
    console.log('User authentication system is now available:');
    console.log('- POST /api/auth/register - Register new user');
    console.log('- POST /api/auth/login - Login user');
    console.log('- GET /api/auth/me - Get current user info');
    
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Check if POSTGRES_URL is set
if (!process.env.POSTGRES_URL) {
  console.error('Error: POSTGRES_URL environment variable is not set.');
  console.error('Please create a .env.local file with your database connection string.');
  console.error('Example: POSTGRES_URL=postgresql://username:password@localhost:5432/talent_mapping');
  process.exit(1);
}

setupDatabase();
