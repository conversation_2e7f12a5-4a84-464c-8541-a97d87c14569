import { NextRequest, NextResponse } from 'next/server';
import { validateUserCredentials, createUserSession, linkSessionToUser } from '@/lib/database';
import { UserLogin } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body: UserLogin = await request.json();
    
    // Validate required fields
    if (!body.name || !body.password) {
      return NextResponse.json(
        { error: 'Name and password are required' },
        { status: 400 }
      );
    }
    
    // Validate user credentials
    const user = await validateUserCredentials(body.name, body.password);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }
    
    // Check if there's an existing session ID in the request
    const existingSessionId = request.headers.get('x-session-id');
    let sessionId: string;

    if (existingSessionId) {
      // Link existing session to user
      console.log('Linking existing session to user:', { sessionId: existingSessionId, userId: user.id });
      await linkSessionToUser(existingSessionId, user.id);
      sessionId = existingSessionId;
    } else {
      // Create new session for user
      console.log('Creating new session for user:', { userId: user.id });
      sessionId = await createUserSession(user.id);
    }

    console.log('Login successful:', { userId: user.id, sessionId, userName: user.name });
    
    return NextResponse.json({
      message: 'Login successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        birth_date: user.birth_date,
        school: user.school,
        created_at: user.created_at
      },
      sessionId
    }, { status: 200 });
    
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
