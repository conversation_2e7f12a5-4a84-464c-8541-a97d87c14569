import { NextRequest, NextResponse } from 'next/server';
import { createUser, getUserByName } from '@/lib/database';
import { UserRegistration } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body: UserRegistration = await request.json();
    
    // Validate required fields
    if (!body.name || !body.password) {
      return NextResponse.json(
        { error: 'Name and password are required' },
        { status: 400 }
      );
    }
    
    // Validate name length
    if (body.name.length < 3 || body.name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 3 and 100 characters' },
        { status: 400 }
      );
    }
    
    // Validate password length
    if (body.password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    // Check if user already exists
    const existingUser = await getUserByName(body.name);
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this name already exists' },
        { status: 409 }
      );
    }
    
    // Validate email format if provided
    if (body.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }
    
    // Validate birth_date if provided
    if (body.birth_date) {
      const birthDate = new Date(body.birth_date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        return NextResponse.json(
          { error: 'Invalid birth date' },
          { status: 400 }
        );
      }
    }
    
    // Create user
    const user = await createUser(body);
    
    return NextResponse.json({
      message: 'User created successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        birth_date: user.birth_date,
        school: user.school,
        created_at: user.created_at
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes('unique constraint')) {
      return NextResponse.json(
        { error: 'User with this name already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
