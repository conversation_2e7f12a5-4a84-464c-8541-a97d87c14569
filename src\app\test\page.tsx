'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAssessmentFlow } from '@/store';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function TestPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session');

  const {
    currentSessionId,
    riasecQuestions,
    oceanQuestions,
    responseScale,
    questionsLoaded,
    riasecResponses,
    oceanResponses,
    setCurrentSession,
    setQuestions,
    setRiasecResponse,
    setOceanResponse,
    clearResponses
  } = useAssessmentFlow();

  const [currentPhase, setCurrentPhase] = useState<'riasec' | 'ocean'>('riasec');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showLoginBanner, setShowLoginBanner] = useState(false);

  useEffect(() => {
    if (!sessionId) {
      router.push('/');
      return;
    }

    // Set current session
    setCurrentSession(sessionId);

    // Clear previous responses if this is a new session
    if (currentSessionId !== sessionId) {
      clearResponses();
    }

    // Check if user is logged in
    checkLoginStatus();

    // Load questions if not already loaded
    if (!questionsLoaded) {
      loadQuestions();
    } else {
      setIsLoading(false);
    }
  }, [sessionId, router, currentSessionId, questionsLoaded]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {

      const questions = getCurrentQuestions();
      const responses = getCurrentResponses();
      const currentQuestion = questions[currentQuestionIndex];

      // Number keys 1-5 for responses
      if (event.key >= '1' && event.key <= '5') {
        const value = parseInt(event.key);
        setCurrentResponse(currentQuestion.id, value);
      }

      // Arrow keys for navigation
      if (event.key === 'ArrowRight' || event.key === 'Enter') {
        if (responses[currentQuestion?.id] !== undefined) {
          handleNext();
        }
      }

      if (event.key === 'ArrowLeft') {
        handlePrevious();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentQuestionIndex, currentPhase, riasecResponses, oceanResponses]);

  const checkLoginStatus = async () => {
    try {
      const existingSessionId = localStorage.getItem('sessionId');
      if (!existingSessionId) {
        setShowLoginBanner(true);
        return;
      }

      const response = await fetch('/api/auth/me', {
        headers: {
          'x-session-id': existingSessionId,
        },
      });

      if (response.ok) {
        setIsLoggedIn(true);
        setShowLoginBanner(false);
      } else {
        setIsLoggedIn(false);
        setShowLoginBanner(true);
      }
    } catch (error) {
      console.error('Error checking login status:', error);
      setIsLoggedIn(false);
      setShowLoginBanner(true);
    }
  };

  const loadQuestions = async () => {
    try {
      const [riasecResponse, oceanResponse] = await Promise.all([
        fetch('/api/questions/riasec'),
        fetch('/api/questions/ocean')
      ]);

      const riasecData = await riasecResponse.json();
      const oceanData = await oceanResponse.json();

      if (riasecData.success && oceanData.success) {
        setQuestions(riasecData.questions, oceanData.questions, riasecData.responseScale);
      } else {
        alert('Failed to load questions');
        router.push('/');
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      alert('Failed to load questions');
      router.push('/');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentQuestions = () => {
    return currentPhase === 'riasec' ? riasecQuestions : oceanQuestions;
  };

  const getCurrentResponses = () => {
    return currentPhase === 'riasec' ? riasecResponses : oceanResponses;
  };

  const setCurrentResponse = (questionId: number, value: number) => {
    if (currentPhase === 'riasec') {
      setRiasecResponse(questionId, value);
    } else {
      setOceanResponse(questionId, value);
    }

    // Auto-advance to next question after a short delay
    setTimeout(() => {
      const questions = getCurrentQuestions();
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      } else if (currentPhase === 'riasec') {
        setCurrentPhase('ocean');
        setCurrentQuestionIndex(0);
      }
    }, 800);
  };

  const handleNext = () => {
    const questions = getCurrentQuestions();

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else if (currentPhase === 'riasec') {
      // Move to OCEAN phase
      setCurrentPhase('ocean');
      setCurrentQuestionIndex(0);
    } else {
      // Confirm before submitting
      if (confirm('Are you ready to submit your assessment? This will generate your personalized results.')) {
        submitTest();
      }
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    } else if (currentPhase === 'ocean') {
      // Go back to RIASEC phase
      setCurrentPhase('riasec');
      setCurrentQuestionIndex(riasecQuestions.length - 1);
    }
  };

  const submitTest = () => {
    // Validate that we have all responses
    if (Object.keys(riasecResponses).length !== 10 || Object.keys(oceanResponses).length !== 10) {
      alert('Please complete all questions before submitting.');
      return;
    }

    // Immediately redirect to processing page
    // The processing page will handle the actual API call
    router.push(`/processing?session=${sessionId}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <LoadingSpinner size="lg" text="Loading assessment questions..." />
      </div>
    );
  }

  const questions = getCurrentQuestions();
  const responses = getCurrentResponses();
  const currentQuestion = questions[currentQuestionIndex];
  const totalQuestions = riasecQuestions.length + oceanQuestions.length;
  const currentOverallIndex = currentPhase === 'riasec' 
    ? currentQuestionIndex 
    : riasecQuestions.length + currentQuestionIndex;

  const canProceed = currentQuestion && responses[currentQuestion.id] !== undefined;
  const isLastQuestion = currentPhase === 'ocean' && currentQuestionIndex === oceanQuestions.length - 1;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Login Banner */}
      {showLoginBanner && !isLoggedIn && (
        <div className="bg-indigo-600 text-white px-6 py-3">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">
                <strong>Save your results!</strong> Login to link this assessment to your account.
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <a
                href={`/auth/login?session=${sessionId}`}
                className="bg-white text-indigo-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors"
              >
                Login
              </a>
              <button
                onClick={() => setShowLoginBanner(false)}
                className="text-indigo-200 hover:text-white"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto px-6 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <div>
              <span className="text-sm font-medium text-gray-700">
                {currentPhase === 'riasec' ? 'RIASEC Assessment' : 'OCEAN Assessment'}
              </span>
              <span className="text-xs text-gray-500 block">
                {currentPhase === 'riasec' ? 'Career Interests' : 'Personality Traits'}
              </span>
            </div>
            <span className="text-sm text-gray-500">
              {currentOverallIndex + 1} of {totalQuestions}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-indigo-500 to-indigo-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentOverallIndex + 1) / totalQuestions) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>Start</span>
            <span>{Math.round(((currentOverallIndex + 1) / totalQuestions) * 100)}% Complete</span>
            <span>Finish</span>
          </div>
        </div>

        {/* Question Card */}
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6 md:p-8">
            <div className="mb-4">
              <span className="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">
                Question {currentQuestionIndex + 1} of {getCurrentQuestions().length}
              </span>
            </div>
            <h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-6 leading-relaxed">
              {currentQuestion?.question_text}
            </h2>

            {/* Response Options */}
            <div className="space-y-3 mb-8">
              {responseScale.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                    responses[currentQuestion?.id] === option.value
                      ? 'border-indigo-600 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion?.id}`}
                    value={option.value}
                    checked={responses[currentQuestion?.id] === option.value}
                    onChange={() => setCurrentResponse(currentQuestion.id, option.value)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                    responses[currentQuestion?.id] === option.value
                      ? 'border-indigo-600 bg-indigo-600'
                      : 'border-gray-300'
                  }`}>
                    {responses[currentQuestion?.id] === option.value && (
                      <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                    )}
                  </div>
                  <span className="text-gray-700 flex-1">{option.label}</span>
                  <span className="text-xs text-gray-400 ml-2 hidden sm:block">
                    Press {option.value}
                  </span>
                </label>
              ))}
            </div>

            {/* Keyboard Hints */}
            <div className="text-center mb-6">
              <p className="text-sm text-gray-500">
                💡 Use number keys (1-5) to select answers, arrow keys to navigate
              </p>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentPhase === 'riasec' && currentQuestionIndex === 0}
                className="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>

              <button
                onClick={handleNext}
                disabled={!canProceed}
                className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLastQuestion ? 'Submit Test' : 'Next'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
