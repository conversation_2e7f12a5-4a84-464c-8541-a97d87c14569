require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');

async function migrateDatabase() {
  const pool = new Pool({
    connectionString: process.env.POSTGRES_URL,
  });

  try {
    console.log('Starting database migration...');
    
    // Check if users table exists
    const checkUsersTable = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    if (!checkUsersTable.rows[0].exists) {
      console.log('Creating users table...');
      await pool.query(`
        CREATE TABLE users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(100) NOT NULL UNIQUE,
          password_hash VARCHAR(255) NOT NULL,
          email VARCHAR(255),
          birth_date DATE,
          school VARCHAR(255),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✓ Users table created');
    } else {
      console.log('✓ Users table already exists');
    }
    
    // Check if user_id column exists in user_sessions
    const checkUserIdColumn = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_sessions' 
        AND column_name = 'user_id'
      );
    `);
    
    if (!checkUserIdColumn.rows[0].exists) {
      console.log('Adding user_id column to user_sessions table...');
      await pool.query(`
        ALTER TABLE user_sessions 
        ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
      `);
      console.log('✓ user_id column added to user_sessions');
    } else {
      console.log('✓ user_id column already exists in user_sessions');
    }
    
    // Create indexes if they don't exist
    console.log('Creating indexes...');
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
    `);
    
    console.log('✓ Indexes created');
    
    console.log('');
    console.log('Database migration completed successfully!');
    console.log('');
    console.log('User authentication system is now available:');
    console.log('- POST /api/auth/register - Register new user');
    console.log('- POST /api/auth/login - Login user');
    console.log('- GET /api/auth/me - Get current user info');
    console.log('');
    console.log('Frontend pages available:');
    console.log('- /auth/register - Registration page');
    console.log('- /auth/login - Login page');
    
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Check if POSTGRES_URL is set
if (!process.env.POSTGRES_URL) {
  console.error('Error: POSTGRES_URL environment variable is not set.');
  console.error('Please create a .env.local file with your database connection string.');
  console.error('Example: POSTGRES_URL=postgresql://username:password@localhost:5432/talent_mapping');
  process.exit(1);
}

migrateDatabase();
