import { NextRequest, NextResponse } from 'next/server';
import {
  saveRiasecResponses,
  saveOceanResponses,
  calculateRiasecScores,
  calculateOceanScores,
  saveAssessmentScores,
  saveAnalysisResult
} from '@/lib/database';
import { generatePersonalityAnalysis } from '@/lib/ai-analysis';
import { aiRateLimiter, getClientIdentifier } from '@/lib/rate-limiter';

interface SubmitTestRequest {
  sessionId: string;
  riasecResponses: Array<{
    question_id: number;
    response_value: number;
  }>;
  oceanResponses: Array<{
    question_id: number;
    response_value: number;
  }>;
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for AI requests
    const clientId = getClientIdentifier(request);
    if (!aiRateLimiter.isAllowed(clientId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((aiRateLimiter.getResetTime(clientId) - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    const body: SubmitTestRequest = await request.json();
    const { sessionId, riasecResponses, oceanResponses } = body;

    // Validate input
    if (!sessionId || !riasecResponses || !oceanResponses) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (riasecResponses.length !== 10 || oceanResponses.length !== 10) {
      return NextResponse.json(
        { success: false, error: 'Invalid number of responses' },
        { status: 400 }
      );
    }

    // Validate response values
    const validateResponses = (responses: Array<{question_id: number; response_value: number}>) => {
      return responses.every(r =>
        typeof r.question_id === 'number' &&
        typeof r.response_value === 'number' &&
        r.response_value >= 1 &&
        r.response_value <= 5
      );
    };

    if (!validateResponses(riasecResponses) || !validateResponses(oceanResponses)) {
      return NextResponse.json(
        { success: false, error: 'Invalid response values' },
        { status: 400 }
      );
    }

    // Save responses to database
    await Promise.all([
      saveRiasecResponses(sessionId, riasecResponses),
      saveOceanResponses(sessionId, oceanResponses)
    ]);

    // Calculate scores
    const [riasecScores, oceanScores] = await Promise.all([
      calculateRiasecScores(sessionId),
      calculateOceanScores(sessionId)
    ]);

    // Save calculated scores
    await saveAssessmentScores(sessionId, riasecScores, oceanScores);

    // Generate AI analysis
    const startTime = Date.now();
    const analysis = await generatePersonalityAnalysis(riasecScores, oceanScores);
    const processingTime = Date.now() - startTime;

    // Save analysis results
    await saveAnalysisResult(sessionId, analysis, 'gemini-2.0-flash-exp', processingTime);

    return NextResponse.json({
      success: true,
      message: 'Test submitted and analyzed successfully',
      sessionId
    });

  } catch (error) {
    console.error('Error submitting test:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process test submission' },
      { status: 500 }
    );
  }
}
