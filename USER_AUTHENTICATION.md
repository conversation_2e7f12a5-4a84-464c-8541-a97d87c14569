# User Authentication System Integration

## Overview

Sistem user authentication telah berhasil diintegrasikan dengan database talent mapping yang sudah ada. Sistem ini memungkinkan user untuk membuat akun, login, dan menghubungkan hasil assessment dengan akun mereka.

## Database Schema Changes

### New Table: `users`
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    birth_date DATE,
    school VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Modified Table: `user_sessions`
```sql
-- Added user_id column to link sessions with user accounts
ALTER TABLE user_sessions 
ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
```

## Required Fields vs Optional Fields

### Required Fields (untuk registrasi):
- `name`: <PERSON><PERSON> user (3-100 karakter, harus unique)
- `password`: Password (minimal 6 karakter)

### Optional Fields:
- `email`: Email address (dengan validasi format)
- `birth_date`: Tanggal lahir (validasi umur 13-120 tahun)
- `school`: Asal sekolah

## API Endpoints

### 1. User Registration
**POST** `/api/auth/register`

**Request Body:**
```json
{
  "name": "john_doe",
  "password": "password123",
  "email": "<EMAIL>",        // optional
  "birth_date": "1995-05-15",         // optional
  "school": "University of Example"   // optional
}
```

**Response (Success):**
```json
{
  "message": "User created successfully",
  "user": {
    "id": "uuid",
    "name": "john_doe",
    "email": "<EMAIL>",
    "birth_date": "1995-05-15",
    "school": "University of Example",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. User Login
**POST** `/api/auth/login`

**Request Headers:**
```
x-session-id: existing-session-id  // optional, untuk link existing session
```

**Request Body:**
```json
{
  "name": "john_doe",
  "password": "password123"
}
```

**Response (Success):**
```json
{
  "message": "Login successful",
  "user": {
    "id": "uuid",
    "name": "john_doe",
    "email": "<EMAIL>",
    "birth_date": "1995-05-15",
    "school": "University of Example",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "sessionId": "session-uuid"
}
```

### 3. Get Current User Info
**GET** `/api/auth/me`

**Request Headers:**
```
x-session-id: session-uuid  // required
```

**Response (Success):**
```json
{
  "user": {
    "id": "uuid",
    "name": "john_doe",
    "email": "<EMAIL>",
    "birth_date": "1995-05-15",
    "school": "University of Example",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## Frontend Pages

### Registration Page
- URL: `/auth/register`
- Form dengan semua fields (name dan password required)
- Validasi client-side dan server-side
- Redirect ke login page setelah sukses

### Login Page
- URL: `/auth/login`
- Form dengan name dan password
- Support untuk link existing anonymous session
- Redirect ke home page setelah sukses

## Integration with Existing System

### Session Management
1. **Anonymous Sessions**: Tetap didukung untuk backward compatibility
2. **User Sessions**: Session bisa di-link dengan user account
3. **Session Linking**: Jika user login dengan existing session, session akan di-link ke user

### Assessment Results
- Assessment results tetap tersimpan berdasarkan session_id
- Jika session di-link dengan user, results otomatis terhubung dengan user account
- User bisa melihat semua assessment results dari sessions yang terhubung dengan account mereka

## Security Features

### Password Security
- Password di-hash menggunakan bcrypt dengan salt rounds 10
- Password tidak pernah disimpan dalam plain text
- Password tidak dikembalikan dalam response API

### Input Validation
- Name: 3-100 karakter, unique
- Password: minimal 6 karakter
- Email: validasi format email
- Birth date: validasi umur 13-120 tahun

### Database Security
- Foreign key constraints untuk data integrity
- Cascade delete untuk cleanup otomatis
- Indexes untuk performa query

## Migration Script

Untuk database yang sudah ada, jalankan:
```bash
node scripts/migrate-add-users.js
```

Script ini akan:
1. Membuat tabel `users` jika belum ada
2. Menambahkan kolom `user_id` ke tabel `user_sessions` jika belum ada
3. Membuat indexes yang diperlukan

## Testing

1. **Start development server:**
   ```bash
   npm run dev
   ```

2. **Test registration:**
   - Buka http://localhost:3000/auth/register
   - Isi form dengan name dan password (fields lain optional)
   - Submit form

3. **Test login:**
   - Buka http://localhost:3000/auth/login
   - Login dengan credentials yang sudah dibuat

4. **Test API directly:**
   ```bash
   # Register
   curl -X POST http://localhost:3000/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{"name":"testuser","password":"password123"}'
   
   # Login
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"name":"testuser","password":"password123"}'
   ```

## Next Steps

1. **Frontend Integration**: Update existing components untuk support user authentication
2. **User Dashboard**: Buat dashboard untuk melihat assessment history
3. **Profile Management**: Buat halaman untuk edit profile user
4. **Session Management**: Implement logout functionality
5. **Assessment Linking**: Update assessment flow untuk otomatis link dengan user account
