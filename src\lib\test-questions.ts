// RIASEC Test Questions
export const RIASEC_QUESTIONS = [
  {
    id: 1,
    question_text: "Saya senang bekerja dengan alat-alat dan mesin",
    category: "R" as const,
    description: "Realistic - Praktis, hands-on, bekerja dengan benda konkret"
  },
  {
    id: 2,
    question_text: "Saya suka memecahkan masalah dan teka-teki yang kompleks",
    category: "I" as const,
    description: "Investigative - Analitis, penelitian, pemecahan masalah"
  },
  {
    id: 3,
    question_text: "Saya senang menciptakan seni, musik, atau menulis",
    category: "A" as const,
    description: "Artistic - Kreatif, ekspresif, imajinatif"
  },
  {
    id: 4,
    question_text: "Saya suka membantu dan mengajar orang lain",
    category: "S" as const,
    description: "Social - Membantu, mengajar, bekerja dengan orang"
  },
  {
    id: 5,
    question_text: "Saya senang memimpin tim dan membuat keputusan",
    category: "E" as const,
    description: "Enterprising - Kepemimpinan, persuasi, bisnis"
  },
  {
    id: 6,
    question_text: "Saya lebih suka lingkungan kerja yang terorganisir dan terstruktur",
    category: "C" as const,
    description: "Conventional - Terorganisir, detail, prosedural"
  },
  {
    id: 7,
    question_text: "Saya suka bekerja di luar ruangan dan menggunakan tangan saya",
    category: "R" as const,
    description: "Realistic - Praktis, hands-on, bekerja dengan benda konkret"
  },
  {
    id: 8,
    question_text: "Saya senang melakukan penelitian dan eksperimen",
    category: "I" as const,
    description: "Investigative - Analitis, penelitian, pemecahan masalah"
  },
  {
    id: 9,
    question_text: "Saya suka mengekspresikan diri secara kreatif",
    category: "A" as const,
    description: "Artistic - Kreatif, ekspresif, imajinatif"
  },
  {
    id: 10,
    question_text: "Saya senang memberikan konseling dan dukungan kepada orang",
    category: "S" as const,
    description: "Social - Membantu, mengajar, bekerja dengan orang"
  }
];

// OCEAN Test Questions
export const OCEAN_QUESTIONS = [
  {
    id: 1,
    question_text: "Saya adalah seseorang yang original dan suka menghasilkan ide-ide baru",
    category: "O" as const,
    reverse_scored: false,
    description: "Openness - Keterbukaan terhadap pengalaman baru"
  },
  {
    id: 2,
    question_text: "Saya adalah seseorang yang melakukan pekerjaan dengan teliti",
    category: "C" as const,
    reverse_scored: false,
    description: "Conscientiousness - Kehati-hatian dan kedisiplinan"
  },
  {
    id: 3,
    question_text: "Saya adalah seseorang yang suka berbicara",
    category: "E" as const,
    reverse_scored: false,
    description: "Extraversion - Kecenderungan untuk bersosialisasi"
  },
  {
    id: 4,
    question_text: "Saya adalah seseorang yang suka menolong dan tidak egois terhadap orang lain",
    category: "A" as const,
    reverse_scored: false,
    description: "Agreeableness - Keramahan dan kerjasama"
  },
  {
    id: 5,
    question_text: "Saya adalah seseorang yang bisa menjadi tegang",
    category: "N" as const,
    reverse_scored: false,
    description: "Neuroticism - Kecenderungan emosi negatif"
  },
  {
    id: 6,
    question_text: "Saya adalah seseorang yang melakukan hal-hal dengan efisien",
    category: "C" as const,
    reverse_scored: false,
    description: "Conscientiousness - Kehati-hatian dan kedisiplinan"
  },
  {
    id: 7,
    question_text: "Saya adalah seseorang yang pendiam",
    category: "E" as const,
    reverse_scored: true,
    description: "Extraversion - Kecenderungan untuk bersosialisasi (reverse)"
  },
  {
    id: 8,
    question_text: "Saya adalah seseorang yang pemaaf",
    category: "A" as const,
    reverse_scored: false,
    description: "Agreeableness - Keramahan dan kerjasama"
  },
  {
    id: 9,
    question_text: "Saya adalah seseorang yang bisa menjadi murung",
    category: "N" as const,
    reverse_scored: false,
    description: "Neuroticism - Kecenderungan emosi negatif"
  },
  {
    id: 10,
    question_text: "Saya adalah seseorang yang menghargai pengalaman artistik dan estetika",
    category: "O" as const,
    reverse_scored: false,
    description: "Openness - Keterbukaan terhadap pengalaman baru"
  }
];

// Response scale for both tests
export const RESPONSE_SCALE = [
  { value: 1, label: "Sangat Tidak Setuju" },
  { value: 2, label: "Tidak Setuju" },
  { value: 3, label: "Netral" },
  { value: 4, label: "Setuju" },
  { value: 5, label: "Sangat Setuju" }
];

// RIASEC Category Descriptions
export const RIASEC_DESCRIPTIONS = {
  R: {
    name: "Realistic",
    description: "Orang dengan tipe Realistic cenderung praktis, suka bekerja dengan tangan, dan menikmati aktivitas fisik. Mereka biasanya menyukai pekerjaan yang melibatkan alat, mesin, atau benda-benda konkret.",
    characteristics: ["Praktis", "Hands-on", "Suka tantangan fisik", "Berorientasi pada hasil", "Suka bekerja dengan alat"],
    careers: ["Teknisi", "Insinyur", "Mekanik", "Petani", "Pilot", "Arsitek"]
  },
  I: {
    name: "Investigative",
    description: "Orang dengan tipe Investigative adalah pemikir analitis yang suka memecahkan masalah kompleks. Mereka cenderung intelektual, suka penelitian, dan menikmati aktivitas yang membutuhkan pemikiran mendalam.",
    characteristics: ["Analitis", "Intelektual", "Suka penelitian", "Pemecah masalah", "Objektif"],
    careers: ["Ilmuwan", "Peneliti", "Dokter", "Psikolog", "Analis Data", "Programmer"]
  },
  A: {
    name: "Artistic",
    description: "Orang dengan tipe Artistic adalah individu kreatif yang suka mengekspresikan diri melalui seni. Mereka cenderung imajinatif, ekspresif, dan menikmati aktivitas yang melibatkan kreativitas.",
    characteristics: ["Kreatif", "Imajinatif", "Ekspresif", "Intuitif", "Independen"],
    careers: ["Seniman", "Desainer", "Penulis", "Musisi", "Aktor", "Fotografer"]
  },
  S: {
    name: "Social",
    description: "Orang dengan tipe Social suka membantu dan bekerja dengan orang lain. Mereka cenderung empati, suka mengajar, dan menikmati aktivitas yang melibatkan interaksi sosial.",
    characteristics: ["Empati", "Suka membantu", "Komunikatif", "Kooperatif", "Peduli"],
    careers: ["Guru", "Konselor", "Perawat", "Pekerja Sosial", "HR", "Terapis"]
  },
  E: {
    name: "Enterprising",
    description: "Orang dengan tipe Enterprising adalah pemimpin alami yang suka mempengaruhi dan meyakinkan orang lain. Mereka cenderung ambisius, energik, dan menikmati tantangan bisnis.",
    characteristics: ["Pemimpin", "Ambisius", "Persuasif", "Energik", "Kompetitif"],
    careers: ["Manajer", "Pengusaha", "Sales", "Lawyer", "Politisi", "Eksekutif"]
  },
  C: {
    name: "Conventional",
    description: "Orang dengan tipe Conventional suka keteraturan dan struktur. Mereka cenderung detail-oriented, terorganisir, dan menikmati pekerjaan yang memiliki prosedur yang jelas.",
    characteristics: ["Terorganisir", "Detail", "Sistematis", "Dapat diandalkan", "Efisien"],
    careers: ["Akuntan", "Administrator", "Sekretaris", "Analis Keuangan", "Auditor", "Clerk"]
  }
};

// OCEAN Category Descriptions
export const OCEAN_DESCRIPTIONS = {
  O: {
    name: "Openness",
    description: "Keterbukaan terhadap pengalaman baru, ide-ide kreatif, dan hal-hal yang tidak konvensional. Orang dengan skor tinggi cenderung imajinatif dan suka eksplorasi.",
    high_traits: ["Kreatif", "Imajinatif", "Suka eksplorasi", "Terbuka pada ide baru", "Artistik"],
    low_traits: ["Praktis", "Konvensional", "Suka rutinitas", "Realistis", "Tradisional"]
  },
  C: {
    name: "Conscientiousness",
    description: "Tingkat kedisiplinan, keteraturan, dan tanggung jawab seseorang. Orang dengan skor tinggi cenderung terorganisir dan dapat diandalkan.",
    high_traits: ["Disiplin", "Terorganisir", "Bertanggung jawab", "Dapat diandalkan", "Pekerja keras"],
    low_traits: ["Spontan", "Fleksibel", "Santai", "Kurang terstruktur", "Impulsif"]
  },
  E: {
    name: "Extraversion",
    description: "Tingkat kecenderungan untuk bersosialisasi dan mencari stimulasi dari lingkungan eksternal. Orang dengan skor tinggi cenderung outgoing dan energik.",
    high_traits: ["Outgoing", "Energik", "Suka bersosialisasi", "Asertif", "Antusias"],
    low_traits: ["Pendiam", "Introspektif", "Suka ketenangan", "Pemalu", "Reflektif"]
  },
  A: {
    name: "Agreeableness",
    description: "Tingkat keramahan, kerjasama, dan kepercayaan terhadap orang lain. Orang dengan skor tinggi cenderung empati dan suka membantu.",
    high_traits: ["Empati", "Kooperatif", "Percaya pada orang", "Altruistik", "Ramah"],
    low_traits: ["Skeptis", "Kompetitif", "Kritis", "Independen", "Tegas"]
  },
  N: {
    name: "Neuroticism",
    description: "Tingkat kecenderungan mengalami emosi negatif seperti kecemasan, depresi, dan ketidakstabilan emosi. Skor rendah menunjukkan stabilitas emosi.",
    high_traits: ["Mudah cemas", "Sensitif", "Mudah stress", "Emosional", "Mudah tersinggung"],
    low_traits: ["Tenang", "Stabil emosi", "Rileks", "Tahan stress", "Optimis"]
  }
};

export type RiasecCategory = 'R' | 'I' | 'A' | 'S' | 'E' | 'C';
export type OceanCategory = 'O' | 'C' | 'E' | 'A' | 'N';
