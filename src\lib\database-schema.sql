-- Database schema for Talent Mapping Application

-- Table for storing user accounts
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    birth_date DATE,
    school VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing user sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing RIASEC test questions
CREATE TABLE IF NOT EXISTS riasec_questions (
    id SERIAL PRIMARY KEY,
    question_text TEXT NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('R', 'I', 'A', 'S', 'E', 'C')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing OCEAN test questions
CREATE TABLE IF NOT EXISTS ocean_questions (
    id SERIAL PRIMARY KEY,
    question_text TEXT NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('O', 'C', 'E', 'A', 'N')),
    reverse_scored BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing RIASEC test responses
CREATE TABLE IF NOT EXISTS riasec_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    question_id INTEGER REFERENCES riasec_questions(id),
    response_value INTEGER NOT NULL CHECK (response_value BETWEEN 1 AND 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing OCEAN test responses
CREATE TABLE IF NOT EXISTS ocean_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    question_id INTEGER REFERENCES ocean_questions(id),
    response_value INTEGER NOT NULL CHECK (response_value BETWEEN 1 AND 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing calculated scores
CREATE TABLE IF NOT EXISTS assessment_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    
    -- RIASEC scores
    realistic_score DECIMAL(5,2),
    investigative_score DECIMAL(5,2),
    artistic_score DECIMAL(5,2),
    social_score DECIMAL(5,2),
    enterprising_score DECIMAL(5,2),
    conventional_score DECIMAL(5,2),
    
    -- OCEAN scores
    openness_score DECIMAL(5,2),
    conscientiousness_score DECIMAL(5,2),
    extraversion_score DECIMAL(5,2),
    agreeableness_score DECIMAL(5,2),
    neuroticism_score DECIMAL(5,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing AI-generated analysis results
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    
    -- AI generated content
    archetype VARCHAR(100),
    short_summary TEXT,
    strengths TEXT[],
    career_suggestions TEXT[],
    insights TEXT,
    weaknesses TEXT[],
    work_environment TEXT,
    
    -- Metadata
    ai_model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample RIASEC questions
INSERT INTO riasec_questions (question_text, category) VALUES
('I enjoy working with tools and machinery', 'R'),
('I like to solve complex problems and puzzles', 'I'),
('I enjoy creating art, music, or writing', 'A'),
('I like helping and teaching others', 'S'),
('I enjoy leading teams and making decisions', 'E'),
('I prefer organized and structured work environments', 'C'),
('I like working outdoors and with my hands', 'R'),
('I enjoy conducting research and experiments', 'I'),
('I like expressing myself creatively', 'A'),
('I enjoy counseling and supporting people', 'S');

-- Insert sample OCEAN questions
INSERT INTO ocean_questions (question_text, category, reverse_scored) VALUES
('I am someone who is original, comes up with new ideas', 'O', FALSE),
('I am someone who does a thorough job', 'C', FALSE),
('I am someone who is talkative', 'E', FALSE),
('I am someone who is helpful and unselfish with others', 'A', FALSE),
('I am someone who can be tense', 'N', FALSE),
('I am someone who does things efficiently', 'C', FALSE),
('I am someone who is reserved', 'E', TRUE),
('I am someone who is forgiving', 'A', FALSE),
('I am someone who can be moody', 'N', FALSE),
('I am someone who values artistic, aesthetic experiences', 'O', FALSE);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_riasec_responses_session_id ON riasec_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_ocean_responses_session_id ON ocean_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_assessment_scores_session_id ON assessment_scores(session_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_session_id ON analysis_results(session_id);
