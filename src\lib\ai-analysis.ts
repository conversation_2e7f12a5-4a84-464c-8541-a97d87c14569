import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores, OceanScores, AnalysisResult } from './database';

const ai = new GoogleGenAI({
  apiKey: process.env.GOOGLE_AI_API_KEY!
});

// Define the structured output schema for the AI analysis
const analysisSchema = {
  type: Type.OBJECT,
  properties: {
    archetype: {
      type: Type.STRING,
      description: "A short unique archetype name that combines RIASEC and OCEAN traits (e.g., 'The Creative Analyst', 'The Empathetic Leader')"
    },
    short_summary: {
      type: Type.STRING,
      description: "A 2 sentence summary of the person's personality and work style"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "List of 5 key strengths based on the personality profile in a sentence"
    },
    career_suggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "List of 6 career suggestions that align with the personality (write it generally)"
    },
    insights: {
      type: Type.STRING,
      description: "Deeper insights about the person's work style, motivation, and potential growth areas (2 separated paragraphs)"
    },
    weaknesses: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "List of 3-5 potential challenges or areas for development in a sentence"
    },
    work_environment: {
      type: Type.STRING,
      description: "Description of the ideal work environment and conditions for this personality type"
    }
  },
  propertyOrdering: [
    "archetype",
    "short_summary",
    "strengths",
    "career_suggestions",
    "insights",
    "weaknesses",
    "work_environment"
  ]
};

export async function generatePersonalityAnalysis(
  riasecScores: RiasecScores,
  oceanScores: OceanScores
): Promise<AnalysisResult> {
  // Create a detailed prompt with the scores
  const prompt = `## PERAN & TUJUAN

Anda adalah seorang Psychometric Analyst dan Career Strategist yang ahli. Keahlian Anda adalah memberikan analisis bakat secara objektif, jujur, dan berbasis data kepada individu muda yang sedang berada di titik keputusan penting.
Tujuan Anda adalah menyampaikan penilaian yang klinis, apa adanya, dan tidak disaring, berdasarkan data kepribadian yang tersedia, agar siswa SMA dapat memahami karakter dasarnya tanpa dibungkus basa-basi.
Analisis Anda harus bersifat praktis dan menjadi semacam “reality check” yang berguna bagi perencanaan karier di masa depan.

## CONTEXT

Analisis ini ditujukan untuk seorang siswa SMA.
Individu ini membutuhkan umpan balik yang lugas, jelas, dan tidak sentimentil agar dapat mengambil keputusan yang tepat. Hindari basa-basi, kalimat motivasi kosong, atau bahasa yang terlalu membesarkan hati.
Fokuslah pada interpretasi data secara langsung.
Misi utama Anda adalah menyoroti baik keunggulan alami maupun kelemahan signifikan atau area “low-fit” dengan keterusterangan yang sama.
Seperti pepatah, “Hujan dibutuhkan agar bunga dapat tumbuh.”



## METHODOLOGY: THE 4-Phase TALENT ANALYSIS PROTOCOL

Sebelum menghasilkan laporan akhir, Anda harus terlebih dahulu menjalankan proses berpikir terstruktur berikut secara internal.
Tujuannya agar analisis yang Anda buat logis dan berbasis bukti.

Phase 1: Kumpulkan Fakta (Apa)
Pertama, kita perlu memahami elemen dasar dari profil individu ini.

Langkah 1: Temukan Minat Mereka.
Lihat hasil RIASEC untuk mengetahui apa yang secara alami disukai oleh individu ini.
Tujuannya adalah menemukan dua atau tiga bidang minat teratas mereka.
Tanyakan pertanyaan sederhana: “Aktivitas seperti apa yang memberi mereka energi?”
Jawabannya berupa daftar singkat yang diurutkan, misalnya: 1st Investigative, 2nd Realistic.

Langkah 2: Pahami Kepribadian Mereka.
Selanjutnya, gunakan skor OCEAN untuk melihat bagaimana individu ini biasanya berperilaku.
Tujuannya adalah memahami cara alami mereka dalam bekerja, berinteraksi, dan menghadapi tekanan.
Tanyakan: “Bagaimana mereka biasanya menghadapi pekerjaan, orang lain, dan stres?”
Jawabannya berupa ringkasan sifat utama mereka, misalnya: Tinggi pada Conscientiousness, Rendah pada Extraversion.

Phase 2: Hubungkan Pola (Mengapa & Bagaimana)
Ini adalah bagian yang paling penting.
Di sini, Anda akan melihat bagaimana minat dan kepribadian mereka saling mendukung — atau justru bertentangan.

Langkah 1: Temukan Kecocokan Alami.
Cari di mana kepribadian mereka secara alami memperkuat minat mereka.
Tanyakan: “Bagaimana kepribadian mereka membuat mereka lebih baik dalam hal-hal yang mereka sukai?”
Contohnya, memiliki sifat sangat terorganisir (trait) adalah keunggulan besar bagi seseorang yang tertarik pada riset detail (interest).

Langkah 2: Temukan Titik Gesekan.
Cari di mana kepribadian mereka bisa menjadi hambatan atau bertabrakan dengan minat mereka.
Tanyakan: “Di mana gaya alami mereka berpotensi berbenturan dengan tuntutan dari minat kariernya?”
Contohnya, seseorang yang berminat membantu orang lain (Social) tetapi sangat introvert (kepribadian) kemungkinan akan lebih nyaman membantu secara personal dibandingkan berbicara di depan banyak orang.

Phase 3: Buat Profil (Siapa)
Berdasarkan kecocokan dan gesekan tadi, saatnya membangun identitas profesional yang jelas dan akurat.

Langkah 1: Tuliskan Core Story Mereka.
Dengan satu atau dua kalimat, rangkum dinamika utama yang Anda temukan.
Cerita ini harus menangkap esensi profesional mereka.
Contohnya: “Individu ini adalah pemecah masalah alami yang senang bekerja dengan fokus tinggi, namun menghasilkan karya terbaik saat bekerja sendiri.”

Langkah 2: Berikan Mereka Sebuah Archetype.
Berdasarkan core story tadi, buatlah sebutan yang sederhana dan mudah diingat.
Sebutan ini harus bisa langsung mewakili diri mereka.
Contohnya: The Independent Specialist, The Disciplined Creator, atau The Quiet Helper.

Phase 4: Susun Strategi (Di Mana & Selanjutnya)
Terakhir, ubah semua insight ini menjadi saran yang praktis dan berorientasi ke depan.

Langkah 1: Tentukan Ideal Work Zone Mereka.
Jelaskan pekerjaan dan lingkungan yang paling cocok bagi mereka.
Daftarkan peran-peran di mana kekuatan mereka akan bersinar, dan area gesekan mereka tidak terlalu berpengaruh.
Tegaskan kekuatan utama mereka yang sebaiknya mereka tonjolkan kepada orang lain.

Langkah 2: Identifikasi Area Pengembangan dan Peringatan Dini.
Soroti tantangan spesifik yang mungkin mereka hadapi, serta peran atau lingkungan yang sebaiknya dihindari.
Jangan menyebutnya sebagai kelemahan, tetapi sebagai “peringatan” atau “watchouts.”
Lalu, sarankan keterampilan yang bisa mereka kembangkan untuk meningkatkan peluang sukses mereka.

## OUTPUT REQUIREMENTS

Write the output in Bahasa Indonesia except for the career suggestions. The career suggestions should be in English.

## INPUT DATA

**RIASEC Scores (Holland Code):**
- Realistic (R): ${riasecScores.realistic.toFixed(2)}/5.0
- Investigative (I): ${riasecScores.investigative.toFixed(2)}/5.0
- Artistic (A): ${riasecScores.artistic.toFixed(2)}/5.0
- Social (S): ${riasecScores.social.toFixed(2)}/5.0
- Enterprising (E): ${riasecScores.enterprising.toFixed(2)}/5.0
- Conventional (C): ${riasecScores.conventional.toFixed(2)}/5.0

**OCEAN Scores (Big Five):**
- Openness (O): ${oceanScores.openness.toFixed(2)}/5.0
- Conscientiousness (C): ${oceanScores.conscientiousness.toFixed(2)}/5.0
- Extraversion (E): ${oceanScores.extraversion.toFixed(2)}/5.0
- Agreeableness (A): ${oceanScores.agreeableness.toFixed(2)}/5.0
- Neuroticism (N): ${oceanScores.neuroticism.toFixed(2)}/5.0

## Prinsip Penulisan

- Formalitas yang Wajar: Gunakan sapaan dan struktur kalimat yang formal (misalnya, gunakan "Anda" bukan "kamu"), tetapi hindari bahasa yang terlalu birokratis atau kaku yang jarang digunakan dalam percakapan profesional sehari-hari.

- Alur yang Mengalir: Pastikan kalimat-kalimat terhubung secara logis dan nyaman dibaca. Gunakan kata sambung (misalnya: "selain itu", "namun", "oleh karena itu") secara efektif untuk menciptakan alur yang mulus.

- Pilih Kata yang Tepat (Diksi): Pilih padanan kata dalam Bahasa Indonesia yang paling umum dan mudah dipahami dalam konteks profesional, bukan terjemahan harfiah yang mungkin terdengar aneh.

## Aturan Tambahan
Jaga Istilah Teknis: Semua istilah kunci, jargon teknis, dan nama spesifik (jika ada) harus tetap dalam bahasa Inggris. Jangan mencoba menerjemahkannya.
`;

  try {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI request timeout after 90 seconds')), 90000);
    });

    const aiPromise = ai.models.generateContent({
      model: "gemini-2.5-pro",
      contents: prompt,
      config: {
        temperature: 0.7,
        topK: 50,
        topP: 0.8,
        responseMimeType: "application/json",
        responseSchema: analysisSchema,
      },
    });

    const response = await Promise.race([aiPromise, timeoutPromise]) as any;
    const analysisText = response.text;
    if (!analysisText) {
      throw new Error('No response text received from AI');
    }

    const analysis: AnalysisResult = JSON.parse(analysisText);

    return analysis;
  } catch (error) {
    console.error('Error generating AI analysis:', error);
    throw new Error('Failed to generate personality analysis');
  }
}

// Helper function to get the dominant RIASEC type
export function getDominantRiasecType(scores: RiasecScores): string {
  const entries = Object.entries(scores) as [keyof RiasecScores, number][];
  const sorted = entries.sort(([, a], [, b]) => b - a);
  return sorted[0][0];
}

// Helper function to get the top OCEAN traits
export function getTopOceanTraits(scores: OceanScores): string[] {
  const entries = Object.entries(scores) as [keyof OceanScores, number][];
  const sorted = entries.sort(([, a], [, b]) => b - a);
  return sorted.slice(0, 3).map(([trait]) => trait);
}

// Helper function to format scores for display
export function formatScoresForDisplay(riasecScores: RiasecScores, oceanScores: OceanScores) {
  return {
    riasec: {
      realistic: Math.round(riasecScores.realistic * 20), // Convert to percentage
      investigative: Math.round(riasecScores.investigative * 20),
      artistic: Math.round(riasecScores.artistic * 20),
      social: Math.round(riasecScores.social * 20),
      enterprising: Math.round(riasecScores.enterprising * 20),
      conventional: Math.round(riasecScores.conventional * 20)
    },
    ocean: {
      openness: Math.round(oceanScores.openness * 20),
      conscientiousness: Math.round(oceanScores.conscientiousness * 20),
      extraversion: Math.round(oceanScores.extraversion * 20),
      agreeableness: Math.round(oceanScores.agreeableness * 20),
      neuroticism: Math.round(oceanScores.neuroticism * 20)
    }
  };
}
