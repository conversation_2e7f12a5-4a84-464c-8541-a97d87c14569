'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Navbar';

export default function Home() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const startAssessment = async () => {
    setIsLoading(true);
    try {
      // Check if user is already logged in
      const existingSessionId = localStorage.getItem('sessionId');

      if (existingSessionId) {
        // User is logged in, check if session is valid and linked to user
        const userCheckResponse = await fetch('/api/auth/me', {
          headers: {
            'x-session-id': existingSessionId,
          },
        });

        if (userCheckResponse.ok) {
          // User is logged in with valid session, use existing session
          console.log('Using existing user session:', existingSessionId);
          router.push(`/test?session=${existingSessionId}`);
          return;
        } else {
          // Invalid session, clear localStorage
          localStorage.removeItem('sessionId');
          localStorage.removeItem('user');
        }
      }

      // Create new session (will be linked to user if they have a valid session)
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (existingSessionId) {
        headers['x-session-id'] = existingSessionId;
      }

      const response = await fetch('/api/session', {
        method: 'POST',
        headers,
      });

      const data = await response.json();

      if (data.success) {
        // Store the new session ID temporarily (will be linked if user logs in later)
        localStorage.setItem('sessionId', data.sessionId);
        router.push(`/test?session=${data.sessionId}`);
      } else {
        alert('Failed to start assessment. Please try again.');
      }
    } catch (error) {
      console.error('Error starting assessment:', error);
      alert('Failed to start assessment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Navbar />

      {/* Main Content */}
      <main className="container mx-auto px-6 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            AI-Driven Talent Mapping Assessment
          </h1>

          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
            Discover your unique strengths, career preferences, and personality traits with our
            AI-powered assessment combining RIASEC and Big Five personality dimensions.
          </p>

          <button
            onClick={startAssessment}
            disabled={isLoading}
            className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-colors duration-200 shadow-lg hover:shadow-xl mb-16"
          >
            {isLoading ? 'Starting Assessment...' : 'Start Your Assessment'}
          </button>
        </div>

        {/* AI & Structured Synthesis Information */}
        <div className="max-w-6xl mx-auto mt-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Powered by Advanced AI Technology
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our assessment leverages cutting-edge artificial intelligence and structured synthesis
              to provide you with the most accurate and personalized career insights.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* AI Processing */}
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900">AI-Powered Analysis</h3>
              </div>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Google Generative AI:</strong> Utilizes advanced language models for deep personality analysis</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Pattern Recognition:</strong> Identifies complex behavioral patterns from your responses</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Contextual Understanding:</strong> Interprets responses within career and personality frameworks</span>
                </li>
              </ul>
            </div>

            {/* Structured Synthesis */}
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900">Structured Synthesis</h3>
              </div>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Standardized Output:</strong> Consistent, structured format for reliable interpretation</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Multi-dimensional Analysis:</strong> Combines RIASEC interests with Big Five traits</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>Actionable Insights:</strong> Generates specific career recommendations and development areas</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Assessment Flow */}
          <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">How Our AI Assessment Works</h3>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
                <h4 className="font-semibold text-gray-900 mb-2">Data Collection</h4>
                <p className="text-sm text-gray-600">RIASEC & OCEAN questionnaire responses</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
                <h4 className="font-semibold text-gray-900 mb-2">AI Processing</h4>
                <p className="text-sm text-gray-600">Google GenAI analyzes patterns and traits</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
                <h4 className="font-semibold text-gray-900 mb-2">Structured Synthesis</h4>
                <p className="text-sm text-gray-600">Generates standardized personality profile</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">4</div>
                <h4 className="font-semibold text-gray-900 mb-2">Personalized Results</h4>
                <p className="text-sm text-gray-600">Career insights and recommendations</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}