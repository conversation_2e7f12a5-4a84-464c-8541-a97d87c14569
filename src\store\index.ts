import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Question {
  id: number;
  question_text: string;
  category: string;
  reverse_scored?: boolean;
}

interface ResponseScale {
  value: number;
  label: string;
}

interface AssessmentResults {
  sessionId: string;
  riasec: {
    scores: Record<string, number>;
    descriptions: Record<string, any>;
  };
  ocean: {
    scores: Record<string, number>;
    descriptions: Record<string, any>;
  };
  analysis: {
    archetype: string;
    shortSummary: string;
    strengths: string[];
    careerSuggestions: string[];
    insights: string;
    weaknesses: string[];
    workEnvironment: string;
  };
  timestamp: number;
}

interface AssessmentState {
  // Current session
  currentSessionId: string | null;

  // Questions cache
  riasecQuestions: Question[];
  oceanQuestions: Question[];
  responseScale: ResponseScale[];
  questionsLoaded: boolean;

  // Test responses
  riasecResponses: Record<number, number>;
  oceanResponses: Record<number, number>;

  // Results cache
  cachedResults: Record<string, AssessmentResults>;

  // Actions
  setCurrentSession: (sessionId: string) => void;
  setQuestions: (riasec: Question[], ocean: Question[], scale: ResponseScale[]) => void;
  setRiasecResponse: (questionId: number, value: number) => void;
  setOceanResponse: (questionId: number, value: number) => void;
  clearResponses: () => void;
  cacheResults: (sessionId: string, results: AssessmentResults) => void;
  getCachedResults: (sessionId: string) => AssessmentResults | null;
  clearCache: () => void;
}

export const useAssessmentStore = create<AssessmentState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSessionId: null,
      riasecQuestions: [],
      oceanQuestions: [],
      responseScale: [],
      questionsLoaded: false,
      riasecResponses: {},
      oceanResponses: {},
      cachedResults: {},

      // Actions
      setCurrentSession: (sessionId: string) => {
        set({ currentSessionId: sessionId });
      },

      setQuestions: (riasec: Question[], ocean: Question[], scale: ResponseScale[]) => {
        set({
          riasecQuestions: riasec,
          oceanQuestions: ocean,
          responseScale: scale,
          questionsLoaded: true,
        });
      },

      setRiasecResponse: (questionId: number, value: number) => {
        set((state) => ({
          riasecResponses: {
            ...state.riasecResponses,
            [questionId]: value,
          },
        }));
      },

      setOceanResponse: (questionId: number, value: number) => {
        set((state) => ({
          oceanResponses: {
            ...state.oceanResponses,
            [questionId]: value,
          },
        }));
      },

      clearResponses: () => {
        set({
          riasecResponses: {},
          oceanResponses: {},
        });
      },

      cacheResults: (sessionId: string, results: AssessmentResults) => {
        set((state) => ({
          cachedResults: {
            ...state.cachedResults,
            [sessionId]: {
              ...results,
              timestamp: Date.now(),
            },
          },
        }));
      },

      getCachedResults: (sessionId: string) => {
        const results = get().cachedResults[sessionId];
        if (!results) return null;

        // Check if cache is still valid (24 hours)
        const isValid = Date.now() - results.timestamp < 24 * 60 * 60 * 1000;
        return isValid ? results : null;
      },

      clearCache: () => {
        set({ cachedResults: {} });
      },
    }),
    {
      name: 'assessment-storage',
      partialize: (state) => ({
        // Only persist certain parts of the state
        riasecQuestions: state.riasecQuestions,
        oceanQuestions: state.oceanQuestions,
        responseScale: state.responseScale,
        questionsLoaded: state.questionsLoaded,
        cachedResults: state.cachedResults,
      }),
    }
  )
);

// Utility hook for managing assessment flow
export const useAssessmentFlow = () => {
  const store = useAssessmentStore();

  const isRiasecComplete = () => {
    return store.riasecQuestions.length > 0 &&
           Object.keys(store.riasecResponses).length === store.riasecQuestions.length;
  };

  const isOceanComplete = () => {
    return store.oceanQuestions.length > 0 &&
           Object.keys(store.oceanResponses).length === store.oceanQuestions.length;
  };

  const isAssessmentComplete = () => {
    return isRiasecComplete() && isOceanComplete();
  };

  const getProgress = () => {
    const totalQuestions = store.riasecQuestions.length + store.oceanQuestions.length;
    const completedQuestions = Object.keys(store.riasecResponses).length +
                              Object.keys(store.oceanResponses).length;
    return totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0;
  };

  return {
    ...store,
    isRiasecComplete,
    isOceanComplete,
    isAssessmentComplete,
    getProgress,
  };
};
