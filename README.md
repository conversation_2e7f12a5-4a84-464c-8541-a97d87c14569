# Talent Mapping Application

A comprehensive AI-powered talent mapping application that combines RIASEC career interest assessment with Big Five (OCEAN) personality traits to provide personalized career insights and recommendations.

## Features

- **RIASEC Assessment**: Evaluate career interests across six dimensions (Realistic, Investigative, Artistic, Social, Enterprising, Conventional)
- **OCEAN Personality Assessment**: Measure personality traits across five dimensions (Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism)
- **AI-Powered Analysis**: Google Gemini AI generates personalized insights, career recommendations, and work environment preferences
- **Interactive Visualizations**: Radar charts for both RIASEC and OCEAN results
- **Comprehensive Results**: Detailed explanations of each personality dimension and career suggestions
- **Scalable Architecture**: Built with Next.js, TypeScript, and PostgreSQL

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, PostgreSQL
- **AI Integration**: Google Gemini AI with structured output
- **Charts**: Recharts for radar visualizations
- **Database**: PostgreSQL with connection pooling
- **State Management**: <PERSON>ustand

## Prerequisites

- Node.js 18+
- PostgreSQL database
- Google AI API key

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd atma-next
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your configuration:
   ```env
   POSTGRES_URL=postgresql://username:password@localhost:5432/talent_mapping
   GOOGLE_AI_API_KEY=your_google_ai_api_key_here
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up the database**
   ```bash
   npm run setup-db
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Application Flow

1. **Landing Page**: User sees introduction and clicks "Start Assessment"
2. **Session Creation**: System creates a unique session ID
3. **RIASEC Test**: User answers 10 questions about career interests
4. **OCEAN Test**: User answers 10 questions about personality traits
5. **AI Analysis**: Google Gemini AI analyzes responses and generates insights
6. **Results Page**: User views comprehensive results with:
   - RIASEC radar chart and category explanations
   - OCEAN radar chart and trait descriptions
   - AI-generated summary with archetype, strengths, career suggestions, and insights

## API Endpoints

- `POST /api/session` - Create new user session
- `GET /api/questions/riasec` - Get RIASEC questions
- `GET /api/questions/ocean` - Get OCEAN questions
- `POST /api/submit-test` - Submit test responses and trigger AI analysis
- `GET /api/results/[sessionId]` - Get assessment results

## Database Schema

The application uses the following main tables:
- `user_sessions` - User session tracking
- `riasec_questions` & `ocean_questions` - Test questions
- `riasec_responses` & `ocean_responses` - User responses
- `assessment_scores` - Calculated scores
- `analysis_results` - AI-generated insights

## AI Integration

The application uses Google Gemini AI with structured output to generate:
- Unique personality archetype
- Personalized strengths and weaknesses
- Career recommendations
- Work environment preferences
- Detailed insights about work style and motivation

## Customization

### Adding Questions
Questions can be added by modifying:
- `src/lib/test-questions.ts` for static questions
- Database directly for dynamic questions

### Modifying AI Analysis
The AI prompt and schema can be customized in:
- `src/lib/ai-analysis.ts`

### Styling
The application uses Tailwind CSS. Customize styles in:
- `src/app/globals.css`
- Component files

## Deployment

### Environment Setup
Ensure all environment variables are set in your production environment.

### Database Migration
Run the database setup script in production:
```bash
npm run setup-db
```

### Build and Deploy
```bash
npm run build
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please open an issue in the repository.
