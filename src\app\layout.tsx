import type { Metadata } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import ErrorBoundary from "@/components/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Talent Mapping Assessment - Discover Your Career Profile",
  description: "AI-powered talent mapping combining RIASEC career interests and Big Five personality traits for personalized career insights and recommendations.",
  keywords: "talent mapping, career assessment, RIASEC, Big Five, personality test, career guidance",
  authors: [{ name: "Talent Mapping Team" }],
  openGraph: {
    title: "Talent Mapping Assessment",
    description: "Discover your unique talent profile with our AI-powered assessment",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
