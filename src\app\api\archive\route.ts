import { NextRequest, NextResponse } from 'next/server';
import { getUserAssessmentHistory, getUserBySessionId } from '@/lib/database';
import pool from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get session ID from headers
    const sessionId = request.headers.get('x-session-id');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 401 }
      );
    }

    // Get user from session
    const user = await getUserBySessionId(sessionId);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Debug: Check all sessions for this user
    const allUserSessions = await pool.query(
      'SELECT id, user_id, created_at FROM user_sessions WHERE user_id = $1 ORDER BY created_at DESC',
      [user.id]
    );

    // Debug: Check all anonymous sessions with assessment data
    const anonymousSessions = await pool.query(`
      SELECT DISTINCT us.id, us.created_at,
        CASE WHEN ar.id IS NOT NULL THEN 'has_analysis' ELSE 'no_analysis' END as analysis_status,
        CASE WHEN asc.id IS NOT NULL THEN 'has_scores' ELSE 'no_scores' END as scores_status
      FROM user_sessions us
      LEFT JOIN analysis_results ar ON us.id = ar.session_id
      LEFT JOIN assessment_scores asc ON us.id = asc.session_id
      WHERE us.user_id IS NULL
        AND (ar.id IS NOT NULL OR asc.id IS NOT NULL)
      ORDER BY us.created_at DESC
    `);

    // Get user's assessment history
    const assessmentHistory = await getUserAssessmentHistory(user.id);

    console.log('Archive Debug:', {
      userId: user.id,
      userName: user.name,
      userSessions: allUserSessions.rows,
      anonymousSessionsWithData: anonymousSessions.rows,
      historyCount: assessmentHistory.length,
      history: assessmentHistory
    });

    return NextResponse.json({
      success: true,
      data: assessmentHistory
    });

  } catch (error) {
    console.error('Error fetching assessment history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
