import { NextRequest, NextResponse } from 'next/server';
import { getAssessmentResults } from '@/lib/database';
import { formatScoresForDisplay } from '@/lib/ai-analysis';
import { RIASEC_DESCRIPTIONS, OCEAN_DESCRIPTIONS } from '@/lib/test-questions';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Get assessment results from database
    const results = await getAssessmentResults(sessionId);

    if (!results.scores || !results.analysis) {
      return NextResponse.json(
        { success: false, error: 'Results not found or analysis not complete' },
        { status: 404 }
      );
    }

    // Format scores for display
    const riasecScores = {
      realistic: results.scores.realistic_score,
      investigative: results.scores.investigative_score,
      artistic: results.scores.artistic_score,
      social: results.scores.social_score,
      enterprising: results.scores.enterprising_score,
      conventional: results.scores.conventional_score
    };

    const oceanScores = {
      openness: results.scores.openness_score,
      conscientiousness: results.scores.conscientiousness_score,
      extraversion: results.scores.extraversion_score,
      agreeableness: results.scores.agreeableness_score,
      neuroticism: results.scores.neuroticism_score
    };

    const formattedScores = formatScoresForDisplay(riasecScores, oceanScores);

    // Prepare response data
    const responseData = {
      success: true,
      sessionId,
      riasec: {
        scores: formattedScores.riasec,
        rawScores: riasecScores,
        descriptions: RIASEC_DESCRIPTIONS
      },
      ocean: {
        scores: formattedScores.ocean,
        rawScores: oceanScores,
        descriptions: OCEAN_DESCRIPTIONS
      },
      analysis: {
        archetype: results.analysis.archetype,
        shortSummary: results.analysis.short_summary,
        strengths: results.analysis.strengths,
        careerSuggestions: results.analysis.career_suggestions,
        insights: results.analysis.insights,
        weaknesses: results.analysis.weaknesses,
        workEnvironment: results.analysis.work_environment
      },
      metadata: {
        aiModel: results.analysis.ai_model_used,
        processingTime: results.analysis.processing_time_ms,
        createdAt: results.analysis.created_at
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error fetching results:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch results' },
      { status: 500 }
    );
  }
}
