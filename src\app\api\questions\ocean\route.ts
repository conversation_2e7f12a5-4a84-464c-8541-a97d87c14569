import { NextResponse } from 'next/server';
import { OCEAN_QUESTIONS, RESPONSE_SCALE } from '@/lib/test-questions';

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      questions: OCEAN_QUESTIONS,
      responseScale: RESPONSE_SCALE
    });
  } catch (error) {
    console.error('Error fetching OCEAN questions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch questions' },
      { status: 500 }
    );
  }
}
