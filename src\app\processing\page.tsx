'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import LoadingSpinner from '@/components/LoadingSpinner';
import { useAssessmentFlow, useAssessmentStore } from '@/store';

export default function ProcessingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session');

  const { riasecResponses, oceanResponses } = useAssessmentFlow();
  const { cacheResults } = useAssessmentStore();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const steps = [
    { id: 1, title: 'Validating Responses', description: 'Checking your assessment answers...' },
    { id: 2, title: 'Calculating Scores', description: 'Computing RIASEC and OCEAN scores...' },
    { id: 3, title: 'AI Analysis', description: 'Generating personalized insights...' },
    { id: 4, title: 'Finalizing Results', description: 'Preparing your talent profile...' }
  ];

  useEffect(() => {
    if (!sessionId) {
      router.push('/');
      return;
    }

    // Check if we have responses in store
    if (Object.keys(riasecResponses).length === 0 || Object.keys(oceanResponses).length === 0) {
      router.push(`/test?session=${sessionId}`);
      return;
    }

    processAssessment();
  }, [sessionId, router, riasecResponses, oceanResponses]);

  const processAssessment = async () => {
    try {
      // Step 1: Validating
      setCurrentStep(1);
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Calculating
      setCurrentStep(2);
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Step 3: AI Analysis (actual API call)
      setCurrentStep(3);
      
      // Format responses for API
      const riasecResponsesArray = Object.entries(riasecResponses).map(([questionId, value]) => ({
        question_id: parseInt(questionId),
        response_value: value
      }));

      const oceanResponsesArray = Object.entries(oceanResponses).map(([questionId, value]) => ({
        question_id: parseInt(questionId),
        response_value: value
      }));

      const response = await fetch('/api/submit-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          riasecResponses: riasecResponsesArray,
          oceanResponses: oceanResponsesArray
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to process assessment');
      }

      // Step 4: Finalizing - Fetch and cache results
      setCurrentStep(4);

      // Fetch the results immediately and cache them
      const resultsResponse = await fetch(`/api/results/${sessionId}`);
      const resultsData = await resultsResponse.json();

      if (resultsData.success && sessionId) {
        // Cache the results so results page doesn't need to fetch again
        cacheResults(sessionId, resultsData);

        // Small delay for UX
        await new Promise(resolve => setTimeout(resolve, 500));

        // Redirect to results (data already cached)
        router.push(`/results?session=${sessionId}`);
      } else {
        throw new Error('Failed to fetch results');
      }

    } catch (error: any) {
      console.error('Error processing assessment:', error);
      setError(error.message || 'Failed to process assessment');
    }
  };

  const handleRetry = () => {
    setError(null);
    setRetryCount(prev => prev + 1);
    setCurrentStep(1);
    processAssessment();
  };

  const handleBackToTest = () => {
    router.push(`/test?session=${sessionId}`);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Failed</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          
          <div className="space-y-3">
            <button
              onClick={handleRetry}
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Try Again {retryCount > 0 && `(${retryCount + 1})`}
            </button>
            
            <button
              onClick={handleBackToTest}
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Back to Test
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentStepData = steps.find(step => step.id === currentStep) || steps[0];
  const progress = (currentStep / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Processing Your Assessment</h1>
          <p className="text-gray-600">
            Our AI is analyzing your responses to create your personalized talent profile
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Step {currentStep} of {steps.length}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-indigo-500 to-indigo-600 h-3 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Current Step */}
        <div className="text-center mb-8">
          <LoadingSpinner size="lg" />
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {currentStepData.title}
            </h3>
            <p className="text-gray-600">
              {currentStepData.description}
            </p>
          </div>
        </div>

        {/* Steps List */}
        <div className="space-y-3">
          {steps.map((step) => (
            <div 
              key={step.id}
              className={`flex items-center p-3 rounded-lg ${
                step.id < currentStep 
                  ? 'bg-green-50 text-green-800' 
                  : step.id === currentStep 
                    ? 'bg-indigo-50 text-indigo-800' 
                    : 'bg-gray-50 text-gray-500'
              }`}
            >
              <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                step.id < currentStep 
                  ? 'bg-green-500 text-white' 
                  : step.id === currentStep 
                    ? 'bg-indigo-500 text-white' 
                    : 'bg-gray-300 text-gray-600'
              }`}>
                {step.id < currentStep ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <span className="text-sm font-medium">{step.id}</span>
                )}
              </div>
              <div>
                <div className="font-medium">{step.title}</div>
                <div className="text-sm opacity-75">{step.description}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            This usually takes 30-60 seconds. Please don't close this page.
          </p>
        </div>
      </div>
    </div>
  );
}
