'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

interface AssessmentHistoryItem {
  sessionId: string;
  sessionDate: string;
  analysisDate: string;
  riasec: {
    scores: {
      realistic: number;
      investigative: number;
      artistic: number;
      social: number;
      enterprising: number;
      conventional: number;
    };
  };
  ocean: {
    scores: {
      openness: number;
      conscientiousness: number;
      extraversion: number;
      agreeableness: number;
      neuroticism: number;
    };
  };
  analysis: {
    archetype: string;
    shortSummary: string;
    strengths: string[];
    careerSuggestions: string[];
    insights: string;
    weaknesses: string[];
    workEnvironment: string;
  };
  metadata: {
    aiModel: string;
    processingTime: number;
  };
}

export default function ArchivePage() {
  const [assessmentHistory, setAssessmentHistory] = useState<AssessmentHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAssessmentHistory();
  }, []);

  const fetchAssessmentHistory = async () => {
    try {
      const sessionId = localStorage.getItem('sessionId');
      if (!sessionId) {
        setError('Please log in to view your assessment history');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/archive', {
        headers: {
          'x-session-id': sessionId,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch assessment history');
      }

      const data = await response.json();
      setAssessmentHistory(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTopRiasecCategories = (scores: AssessmentHistoryItem['riasec']['scores']) => {
    const categories = [
      { name: 'Realistic', value: scores.realistic, code: 'R' },
      { name: 'Investigative', value: scores.investigative, code: 'I' },
      { name: 'Artistic', value: scores.artistic, code: 'A' },
      { name: 'Social', value: scores.social, code: 'S' },
      { name: 'Enterprising', value: scores.enterprising, code: 'E' },
      { name: 'Conventional', value: scores.conventional, code: 'C' },
    ];
    
    return categories
      .sort((a, b) => b.value - a.value)
      .slice(0, 3)
      .map(cat => cat.code)
      .join('');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-6 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your assessment history...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-6 py-8">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
            <Link
              href="/"
              className="mt-4 inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Go Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Assessment Archive</h1>
          <p className="text-gray-600">View your previous assessment results and track your progress over time.</p>
        </div>

        {assessmentHistory.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-medium text-gray-900 mb-2">No Assessments Found</h3>
              <p className="text-gray-600 mb-6">You haven't completed any assessments yet.</p>
              <Link
                href="/test"
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
              >
                Take Your First Assessment
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {assessmentHistory.map((assessment, index) => (
              <div key={assessment.sessionId} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">
                      Assessment #{assessmentHistory.length - index}
                    </h3>
                    <p className="text-sm text-gray-500">
                      Completed on {formatDate(assessment.sessionDate)}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="bg-indigo-100 text-indigo-800 text-sm font-medium px-3 py-1 rounded-full">
                      {assessment.analysis.archetype}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      RIASEC: {getTopRiasecCategories(assessment.riasec.scores)}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-gray-700">{assessment.analysis.shortSummary}</p>
                </div>

                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Top Strengths</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {assessment.analysis.strengths.slice(0, 3).map((strength, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Career Suggestions</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {assessment.analysis.careerSuggestions.slice(0, 3).map((career, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          {career}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    Session ID: {assessment.sessionId.slice(0, 8)}...
                  </div>
                  <Link
                    href={`/results?session=${assessment.sessionId}`}
                    className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors duration-200"
                  >
                    View Full Results →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
