{"name": "atma-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-database.js"}, "dependencies": {"@google/genai": "^1.9.0", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.0", "next": "15.3.5", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.1.0", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}