import { NextRequest, NextResponse } from 'next/server';
import { createUserSession } from '@/lib/database';

export async function POST() {
  try {
    const sessionId = await createUserSession();
    
    return NextResponse.json({ 
      success: true, 
      sessionId 
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create session' },
      { status: 500 }
    );
  }
}
