import { NextRequest, NextResponse } from 'next/server';
import { createUserSession, getUserBySessionId } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Check if there's an existing session ID in the request
    const existingSessionId = request.headers.get('x-session-id');
    let userId: string | undefined;

    if (existingSessionId) {
      // Check if the existing session is linked to a user
      const user = await getUserBySessionId(existingSessionId);
      if (user) {
        userId = user.id;
        console.log('Creating session for logged-in user:', { userId, userName: user.name });
      }
    }

    // Create new session (linked to user if logged in, anonymous if not)
    const sessionId = await createUserSession(userId);

    console.log('Session created:', { sessionId, userId: userId || 'anonymous' });

    return NextResponse.json({
      success: true,
      sessionId
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create session' },
      { status: 500 }
    );
  }
}
