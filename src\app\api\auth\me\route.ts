import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-session-id');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID required' },
        { status: 401 }
      );
    }
    
    // Get user information from session
    const query = `
      SELECT u.id, u.name, u.email, u.birth_date, u.school, u.created_at, u.updated_at
      FROM users u
      JOIN user_sessions us ON u.id = us.user_id
      WHERE us.id = $1
    `;
    
    const result = await pool.query(query, [sessionId]);
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Invalid session or user not found' },
        { status: 401 }
      );
    }
    
    const user = result.rows[0];
    
    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        birth_date: user.birth_date,
        school: user.school,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    }, { status: 200 });
    
  } catch (error) {
    console.error('Get user info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
