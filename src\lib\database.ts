import pool from './db';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

export interface User {
  id: string;
  name: string;
  email?: string;
  birth_date?: Date;
  school?: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserRegistration {
  name: string;
  password: string;
  email?: string;
  birth_date?: Date;
  school?: string;
}

export interface UserLogin {
  name: string;
  password: string;
}

export interface RiasecQuestion {
  id: number;
  question_text: string;
  category: 'R' | 'I' | 'A' | 'S' | 'E' | 'C';
}

export interface OceanQuestion {
  id: number;
  question_text: string;
  category: 'O' | 'C' | 'E' | 'A' | 'N';
  reverse_scored: boolean;
}

export interface TestResponse {
  question_id: number;
  response_value: number;
}

export interface RiasecScores {
  realistic: number;
  investigative: number;
  artistic: number;
  social: number;
  enterprising: number;
  conventional: number;
}

export interface OceanScores {
  openness: number;
  conscientiousness: number;
  extraversion: number;
  agreeableness: number;
  neuroticism: number;
}

export interface AnalysisResult {
  archetype: string;
  short_summary: string;
  strengths: string[];
  career_suggestions: string[];
  insights: string;
  weaknesses: string[];
  work_environment: string;
}

// User management functions
export async function createUser(userData: UserRegistration): Promise<User> {
  const userId = uuidv4();
  const saltRounds = 10;
  const passwordHash = await bcrypt.hash(userData.password, saltRounds);

  const query = `
    INSERT INTO users (id, name, password_hash, email, birth_date, school)
    VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING id, name, email, birth_date, school, created_at, updated_at
  `;

  const result = await pool.query(query, [
    userId,
    userData.name,
    passwordHash,
    userData.email || null,
    userData.birth_date || null,
    userData.school || null
  ]);

  return result.rows[0];
}

export async function getUserByName(name: string): Promise<User | null> {
  const query = `
    SELECT id, name, email, birth_date, school, created_at, updated_at
    FROM users WHERE name = $1
  `;
  const result = await pool.query(query, [name]);
  return result.rows[0] || null;
}

export async function getUserById(id: string): Promise<User | null> {
  const query = `
    SELECT id, name, email, birth_date, school, created_at, updated_at
    FROM users WHERE id = $1
  `;
  const result = await pool.query(query, [id]);
  return result.rows[0] || null;
}

export async function getUserBySessionId(sessionId: string): Promise<User | null> {
  const query = `
    SELECT u.id, u.name, u.email, u.birth_date, u.school, u.created_at, u.updated_at
    FROM users u
    JOIN user_sessions us ON u.id = us.user_id
    WHERE us.id = $1
  `;
  const result = await pool.query(query, [sessionId]);
  return result.rows[0] || null;
}

export async function validateUserCredentials(name: string, password: string): Promise<User | null> {
  const query = `
    SELECT id, name, password_hash, email, birth_date, school, created_at, updated_at
    FROM users WHERE name = $1
  `;
  const result = await pool.query(query, [name]);

  if (result.rows.length === 0) {
    return null;
  }

  const user = result.rows[0];
  const isValidPassword = await bcrypt.compare(password, user.password_hash);

  if (!isValidPassword) {
    return null;
  }

  // Return user without password_hash
  const { password_hash, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

// Create a new user session (can be linked to a user or anonymous)
export async function createUserSession(userId?: string): Promise<string> {
  const sessionId = uuidv4();
  const query = 'INSERT INTO user_sessions (id, user_id) VALUES ($1, $2) RETURNING id';
  const result = await pool.query(query, [sessionId, userId || null]);
  return result.rows[0].id;
}

// Link an existing session to a user (for when user logs in after starting anonymous session)
export async function linkSessionToUser(sessionId: string, userId: string): Promise<void> {
  const query = 'UPDATE user_sessions SET user_id = $1, updated_at = NOW() WHERE id = $2';
  await pool.query(query, [userId, sessionId]);
}

// Get RIASEC questions
export async function getRiasecQuestions(): Promise<RiasecQuestion[]> {
  const query = 'SELECT id, question_text, category FROM riasec_questions ORDER BY id';
  const result = await pool.query(query);
  return result.rows;
}

// Get OCEAN questions
export async function getOceanQuestions(): Promise<OceanQuestion[]> {
  const query = 'SELECT id, question_text, category, reverse_scored FROM ocean_questions ORDER BY id';
  const result = await pool.query(query);
  return result.rows;
}

// Save RIASEC responses
export async function saveRiasecResponses(sessionId: string, responses: TestResponse[]): Promise<void> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    
    for (const response of responses) {
      const query = `
        INSERT INTO riasec_responses (session_id, question_id, response_value) 
        VALUES ($1, $2, $3)
      `;
      await client.query(query, [sessionId, response.question_id, response.response_value]);
    }
    
    await client.query('COMMIT');
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Save OCEAN responses
export async function saveOceanResponses(sessionId: string, responses: TestResponse[]): Promise<void> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    
    for (const response of responses) {
      const query = `
        INSERT INTO ocean_responses (session_id, question_id, response_value) 
        VALUES ($1, $2, $3)
      `;
      await client.query(query, [sessionId, response.question_id, response.response_value]);
    }
    
    await client.query('COMMIT');
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Calculate and save RIASEC scores
export async function calculateRiasecScores(sessionId: string): Promise<RiasecScores> {
  const query = `
    SELECT rq.category, AVG(rr.response_value) as avg_score
    FROM riasec_responses rr
    JOIN riasec_questions rq ON rr.question_id = rq.id
    WHERE rr.session_id = $1
    GROUP BY rq.category
  `;
  
  const result = await pool.query(query, [sessionId]);
  
  const scores: RiasecScores = {
    realistic: 0,
    investigative: 0,
    artistic: 0,
    social: 0,
    enterprising: 0,
    conventional: 0
  };
  
  result.rows.forEach(row => {
    const score = parseFloat(row.avg_score);
    switch (row.category) {
      case 'R': scores.realistic = score; break;
      case 'I': scores.investigative = score; break;
      case 'A': scores.artistic = score; break;
      case 'S': scores.social = score; break;
      case 'E': scores.enterprising = score; break;
      case 'C': scores.conventional = score; break;
    }
  });
  
  return scores;
}

// Calculate and save OCEAN scores
export async function calculateOceanScores(sessionId: string): Promise<OceanScores> {
  const query = `
    SELECT 
      oq.category, 
      oq.reverse_scored,
      AVG(
        CASE 
          WHEN oq.reverse_scored = true THEN 6 - or_table.response_value
          ELSE or_table.response_value
        END
      ) as avg_score
    FROM ocean_responses or_table
    JOIN ocean_questions oq ON or_table.question_id = oq.id
    WHERE or_table.session_id = $1
    GROUP BY oq.category, oq.reverse_scored
  `;
  
  const result = await pool.query(query, [sessionId]);
  
  const scores: OceanScores = {
    openness: 0,
    conscientiousness: 0,
    extraversion: 0,
    agreeableness: 0,
    neuroticism: 0
  };
  
  // Group by category and calculate average
  const categoryScores: { [key: string]: number[] } = {};
  
  result.rows.forEach(row => {
    const score = parseFloat(row.avg_score);
    if (!categoryScores[row.category]) {
      categoryScores[row.category] = [];
    }
    categoryScores[row.category].push(score);
  });
  
  // Calculate final averages
  Object.keys(categoryScores).forEach(category => {
    const avgScore = categoryScores[category].reduce((a, b) => a + b, 0) / categoryScores[category].length;
    switch (category) {
      case 'O': scores.openness = avgScore; break;
      case 'C': scores.conscientiousness = avgScore; break;
      case 'E': scores.extraversion = avgScore; break;
      case 'A': scores.agreeableness = avgScore; break;
      case 'N': scores.neuroticism = avgScore; break;
    }
  });
  
  return scores;
}

// Save calculated scores to database
export async function saveAssessmentScores(
  sessionId: string, 
  riasecScores: RiasecScores, 
  oceanScores: OceanScores
): Promise<void> {
  const query = `
    INSERT INTO assessment_scores (
      session_id, realistic_score, investigative_score, artistic_score, 
      social_score, enterprising_score, conventional_score,
      openness_score, conscientiousness_score, extraversion_score,
      agreeableness_score, neuroticism_score
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
  `;
  
  await pool.query(query, [
    sessionId,
    riasecScores.realistic,
    riasecScores.investigative,
    riasecScores.artistic,
    riasecScores.social,
    riasecScores.enterprising,
    riasecScores.conventional,
    oceanScores.openness,
    oceanScores.conscientiousness,
    oceanScores.extraversion,
    oceanScores.agreeableness,
    oceanScores.neuroticism
  ]);
}

// Save analysis results
export async function saveAnalysisResult(
  sessionId: string,
  analysis: AnalysisResult,
  aiModel: string,
  processingTime: number
): Promise<void> {
  const query = `
    INSERT INTO analysis_results (
      session_id, archetype, short_summary, strengths, career_suggestions,
      insights, weaknesses, work_environment, ai_model_used, processing_time_ms
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
  `;
  
  await pool.query(query, [
    sessionId,
    analysis.archetype,
    analysis.short_summary,
    analysis.strengths,
    analysis.career_suggestions,
    analysis.insights,
    analysis.weaknesses,
    analysis.work_environment,
    aiModel,
    processingTime
  ]);
}

// Get complete assessment results
export async function getAssessmentResults(sessionId: string) {
  const scoresQuery = `
    SELECT * FROM assessment_scores WHERE session_id = $1
  `;

  const analysisQuery = `
    SELECT * FROM analysis_results WHERE session_id = $1
  `;

  const [scoresResult, analysisResult] = await Promise.all([
    pool.query(scoresQuery, [sessionId]),
    pool.query(analysisQuery, [sessionId])
  ]);

  return {
    scores: scoresResult.rows[0] || null,
    analysis: analysisResult.rows[0] || null
  };
}

// Get user's assessment history
export async function getUserAssessmentHistory(userId: string) {
  const query = `
    SELECT
      us.id as session_id,
      us.created_at as session_date,
      ar.archetype,
      ar.short_summary,
      ar.strengths,
      ar.career_suggestions,
      ar.insights,
      ar.weaknesses,
      ar.work_environment,
      ar.ai_model_used,
      ar.processing_time_ms,
      ar.created_at as analysis_date,
      scores.realistic_score,
      scores.investigative_score,
      scores.artistic_score,
      scores.social_score,
      scores.enterprising_score,
      scores.conventional_score,
      scores.openness_score,
      scores.conscientiousness_score,
      scores.extraversion_score,
      scores.agreeableness_score,
      scores.neuroticism_score
    FROM user_sessions us
    LEFT JOIN analysis_results ar ON us.id = ar.session_id
    LEFT JOIN assessment_scores scores ON us.id = scores.session_id
    WHERE us.user_id = $1
      AND ar.id IS NOT NULL
      AND scores.id IS NOT NULL
    ORDER BY us.created_at DESC
  `;

  const result = await pool.query(query, [userId]);

  return result.rows.map(row => ({
    sessionId: row.session_id,
    sessionDate: row.session_date,
    analysisDate: row.analysis_date,
    riasec: {
      scores: {
        realistic: parseFloat(row.realistic_score),
        investigative: parseFloat(row.investigative_score),
        artistic: parseFloat(row.artistic_score),
        social: parseFloat(row.social_score),
        enterprising: parseFloat(row.enterprising_score),
        conventional: parseFloat(row.conventional_score)
      }
    },
    ocean: {
      scores: {
        openness: parseFloat(row.openness_score),
        conscientiousness: parseFloat(row.conscientiousness_score),
        extraversion: parseFloat(row.extraversion_score),
        agreeableness: parseFloat(row.agreeableness_score),
        neuroticism: parseFloat(row.neuroticism_score)
      }
    },
    analysis: {
      archetype: row.archetype,
      shortSummary: row.short_summary,
      strengths: row.strengths,
      careerSuggestions: row.career_suggestions,
      insights: row.insights,
      weaknesses: row.weaknesses,
      workEnvironment: row.work_environment
    },
    metadata: {
      aiModel: row.ai_model_used,
      processingTime: row.processing_time_ms
    }
  }));
}
