import { NextResponse } from 'next/server';
import { RIASEC_QUESTIONS, RESPONSE_SCALE } from '@/lib/test-questions';

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      questions: RIASEC_QUESTIONS,
      responseScale: RESPONSE_SCALE
    });
  } catch (error) {
    console.error('Error fetching RIASEC questions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch questions' },
      { status: 500 }
    );
  }
}
